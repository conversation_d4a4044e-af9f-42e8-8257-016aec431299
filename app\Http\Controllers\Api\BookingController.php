<?php

namespace App\Http\Controllers\Api;

use App\Booking;
use App\Http\Controllers\Controller;
use App\Listing;
use App\Models\BillingAddress;
use App\Models\User;
use App\Notifications\BookingNotification;
use App\Services\BookingService;
use App\Services\WalletService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class BookingController extends Controller
{
    function __construct(protected BookingService $bookingService)
    {
        $this->bookingService = $bookingService;
    }
    function get_bookings(Request $request)
    {
        try{
            $validated = $request->validate([
                'type' => 'required|in:past,current'
            ]);
            $perPage = $request->get('per_page', 10);
            $bookings = $this->bookingService->getBookingByStatus($validated, $perPage);
            if ($bookings->isEmpty()) {
                return api_response_json(true, translate('user_bookings.no_booking_found'), []);
            } else {
                return api_response_json(true, translate('user_bookings.booking_found'), $bookings);
            }
        }catch(\Throwable $th){
            return api_response_json(false, $th->getMessage());
        }
    }

    function get_provider_bookings(Request $request)
    {
        try {
            $bookings = $this->bookingService->getBookingsWithFilters($request);

            if ($bookings->isEmpty()) {
                return api_response(true, "Booking not found", []);
            } else {
                return api_response(true, "Booking found", $bookings);
            }
        } catch (\Throwable $th) {
            return api_response(false, $th->getMessage());
        }
    }
    function download_csv(Request $request)
    {
        try {
            return $this->bookingService->exportBookingsCsv($request);
        } catch (\Throwable $th) {
            return api_response_json(false, $th->getMessage());
        }
    }
    function reserve_booking(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                "listing_id" => "required|integer|exists:listings,id",
                "total_amount" => "required|integer|min:1",
                "check_in" => "required|date_format:Y-m-d",
                "check_out" => "date_format:Y-m-d",
                "basis_type" => "required|in:Tour,Hourly,Daily",
                "adult_number" => "required_if:basis_type,Tour|integer|min:0",
                "child_number" => "required_if:basis_type,Tour|integer|min:0",
                "total_days" => Rule::requiredIf(function () use ($request) {
                    return in_array($request->basis_type, ['Daily', 'Hourly']);
                }). "|integer|min:1",
                "total_hours" => "required_if:basis_type,Hourly|integer|min:1",
                "check_in_time" => "required_if:basis_type,Hourly|date_format:h:i A",
                "check_out_time" => "required_if:basis_type,Hourly|date_format:h:i A",
                "guest" => Rule::requiredIf(function () use ($request) {
                    return in_array($request->basis_type, ['Daily', 'Hourly']);
                }) . "|integer|min:0",
                "street_address" => "required|string|max:255",
                "suit_number" => "required|string|max:50",
                "city" => "required|string|max:100",
                "state" => "required|string|max:100",
                "country" => "required|string|max:100",
                "zip_code" => "required|regex:/^\d{5}(-\d{4})?$/",
            ],
            [
                "basis_type.in" => "The selected basis type is invalid. must be in Tour, Hourly, or Daily",
                "check_in.date_format" => "The check in does not match the format Y-m-d(2024-03-18).",
                "check_out.date_format" => "The check out does not match the format Y-m-d(2024-03-18).",
                "check_in_time.date_format" => "The check in time does not match the format h:i A(12:00 AM).",
                "check_out_time.date_format" => "The check out does not match the format Y-m-d(12:00 AM).",
                "street_address.required" => "Street address is required.",
                "street_address.max" => "Street address may not be greater than 255 characters.",
                "suit_number.required" => "Suite number is required.",
                "city.required" => "City is required.",
                "state.required" => "State is required.",
                "country.required" => "Country is required.",
                "zip_code.required" => "Zip code is required.",
                "zip_code.regex" => "Zip code format is invalid, must be 5 digits or 5 digits followed by a dash and 4 digits.",
            ]
        );
        $validator->after(function ($validator) use ($request) {
            if (!empty($request->check_in) && !empty($request->check_out) && $request->check_in >= $request->check_out) {
                $validator->errors()->add('check_out', 'Check out date must be after check in date.');
            }
            if ($request->check_in <= now('America/Bogota')->toDateString()) {
                $validator->errors()->add('check_in', 'Check in time must be after today date.');
            }
        });
        if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }
        try {
            $listing = Listing::with("detail")->find($request->listing_id);
            if ($listing) {
                if ($listing->detail->basis_type != $request->basis_type) {
                    return api_response(false, "Basis type not match with listing basis type");
                }
                $pending_booking = Booking::where("check_in", $request->check_in)->where("listing_basis", $request->basis_type)->where("user_id", auth()->id())->where("status", 0)->first();
                if ($pending_booking) {
                    return api_response(false, "Already have the booking of {$listing->name} on $pending_booking->check_in");
                }
                // tour total check
                if($listing->detail->basis_type == "Tour"){
                    $total_adult_amount = $listing->detail->adult_price * $request->adult_number;
                    $total_child_amount = $listing->detail->child_price * $request->child_number;
                    $total_tour_amount = $total_adult_amount + $total_child_amount;
                    if($total_tour_amount != $request->total_amount){
                        return api_response(false, "Invalid total amount");
                    }
                }elseif($listing->detail->basis_type == "Hourly"){
                    $total_hours = $request->total_hours * $request->total_days;
                    $per_hour_price = $listing->detail->per_hour;
                    $total_amount = $per_hour_price * $total_hours;
                    if($request->total_amount != $total_amount){
                        return api_response(false, "Invalid total amount total amount cannot be less than actual amount");
                    }
                }elseif($listing->detail->basis_type == "Daily"){
                    $total_days = $request->total_days;
                    $per_day_price = $listing->detail->per_day;
                    $total_amount = $per_day_price * $total_days;
                    if($request->total_amount != $total_amount){
                        return api_response(false, "Invalid total amount total amount cannot be less than actual amount");
                    }
                }
                $reserve = $request->all();
                $booking = new Booking();
                $booking->user_id =  auth()->id();
                $booking->listing_id = $listing->id;
                $booking->provider_id = $listing->user_id;
                $booking->check_in = $request->check_in;
                if (!$request->check_out || $request->check_out == 0) {
                    $booking->check_out = $request->check_in;
                } else {
                    $booking->check_out = $request->check_out;
                }
                // $booking->duration_discount_name = $request->duration_discount_name;
                // $booking->duration_discount_percent = $request->duration_discount_percent;
                // $booking->duration_discount_amount = $request->duration_discount_amount;
                $booking->guest = $request->guest;
                if ($listing->detail->basis_type == "Hourly") {
                    $booking->listing_basis = $listing->detail->basis_type;
                    $booking->total_hours = $reserve["total_hours"] ?? 0;
                    $booking->total_days = $reserve["total_days"] ?? 0;
                    $booking->per_hour = $listing->detail->per_hour;
                    $booking->check_in_time = $reserve["check_in_time"] ?? 0;
                    $booking->check_out_time = $reserve["check_out_time"] ?? 0;
                } elseif ($listing->detail->basis_type == "Daily") {
                    $booking->listing_basis = $listing->detail->basis_type;
                    $booking->per_day = $listing->detail->per_day;
                    $booking->total_days = $reserve["total_days"] ?? 0;
                } elseif ($listing->category_id == 1 && $request->basis_type == "Tour") {
                    $booking->listing_basis = "Tour";
                    $booking->per_adult = $listing->detail->adult_price;
                    $booking->per_child = $listing->detail->child_price;
                    $booking->no_adult = $reserve["adult_number"] ?? 0;
                    $booking->no_child = $reserve["child_number"] ?? 0;
                }
                $booking->total_amount = $request->total_amount;
                $booking->sub_total = $request->sub_total;
                $booking->save();
                if ($booking) {
                    WalletService::deposit($booking->id, $listing->user_id, $request->total_amount);
                    $notification_users = User::whereIn("id", [2, $listing->user_id])->get();
                    auth()->user()->notify(new BookingNotification($booking->id));
                    foreach ($notification_users as $notification_user) {
                        $notification_user->notify(new BookingNotification($booking->id));
                    }
                    $billing_address = new BillingAddress();
                    $billing_address->booking_id = $booking->id;
                    $billing_address->street_address = $request->street_address;
                    $billing_address->suit_number = $request->suit_number;
                    $billing_address->city = $request->city;
                    $billing_address->state = $request->state;
                    $billing_address->country = $request->country;
                    $billing_address->zip_code = $request->zip_code;
                    $billing_address->save();
                    return api_response(true, "Booking Added Successfully", $booking);
                }
            }else{
                return api_response(false, "Listing not found");
            }
        } catch (\Throwable $th) {
            return api_response(false, $th->getMessage());
        }
    }
    function listing_booking($id){
        $booking = Listing::where("user_id", auth()->id())->with("active_bookings")->find($id);
        if ($booking) {
            return api_response(true, "You have " . count($booking->active_bookings??[]). " active bookings of this listing", $booking);
        }else{
            return api_response(false, "Booking Not found", $booking);
        }
    }
}
