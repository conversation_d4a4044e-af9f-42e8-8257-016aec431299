<?php

namespace Tests\Feature;

use Tests\TestCase;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

class DashboardTimezoneTest extends TestCase
{
    /**
     * Test that Colombia timezone is properly configured
     *
     * @return void
     */
    public function test_colombia_timezone_configuration()
    {
        // Test that the app timezone is set to Colombia
        $this->assertEquals('America/Bogota', config('app.timezone'));
        
        // Test that Carbon respects the timezone when explicitly set
        $colombiaTime = Carbon::now('America/Bogota');
        $this->assertEquals('America/Bogota', $colombiaTime->getTimezone()->getName());
        
        // Test date string generation with Colombia timezone
        $testDate = '2024-01-15 10:30:00';
        $carbonDate = Carbon::parse($testDate, 'America/Bogota');
        $this->assertEquals('2024-01-15', $carbonDate->toDateString());
        
        // Test that today() uses the correct timezone when specified
        $today = Carbon::now('America/Bogota')->toDateString();
        $this->assertIsString($today);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $today);
    }
    
    /**
     * Test booking date parsing with Colombia timezone
     *
     * @return void
     */
    public function test_booking_date_parsing_with_timezone()
    {
        // Test check-in and check-out date parsing
        $checkIn = '2024-01-15 14:00:00';
        $checkOut = '2024-01-17 12:00:00';
        
        $checkInDate = Carbon::parse($checkIn)->setTimezone('America/Bogota')->toDateString();
        $checkOutDate = Carbon::parse($checkOut)->setTimezone('America/Bogota')->toDateString();
        
        $this->assertEquals('2024-01-15', $checkInDate);
        $this->assertEquals('2024-01-17', $checkOutDate);
        
        // Test today comparison
        $today = Carbon::now('America/Bogota')->toDateString();
        $tomorrow = Carbon::now('America/Bogota')->addDay()->toDateString();
        
        $this->assertNotEquals($today, $tomorrow);
        $this->assertTrue(Carbon::parse($tomorrow, 'America/Bogota')->isAfter(Carbon::parse($today, 'America/Bogota')));
    }
    
    /**
     * Test hourly slot time parsing with Colombia timezone
     *
     * @return void
     */
    public function test_hourly_slot_parsing_with_timezone()
    {
        $slotDate = '2024-01-15';
        $slotTime = '14:00 - 15:00';
        $slotParts = explode(' - ', $slotTime);
        
        $slotStartTime = Carbon::parse($slotDate . ' ' . $slotParts[0])->setTimezone('America/Bogota');
        $slotEndTime = Carbon::parse($slotDate . ' ' . $slotParts[1])->setTimezone('America/Bogota');
        
        $this->assertEquals('America/Bogota', $slotStartTime->getTimezone()->getName());
        $this->assertEquals('America/Bogota', $slotEndTime->getTimezone()->getName());
        $this->assertTrue($slotEndTime->isAfter($slotStartTime));
        
        // Test midnight crossover
        $midnightSlot = '23:00 - 01:00';
        $midnightParts = explode(' - ', $midnightSlot);
        
        $midnightStart = Carbon::parse($slotDate . ' ' . $midnightParts[0])->setTimezone('America/Bogota');
        $midnightEnd = Carbon::parse($slotDate . ' ' . $midnightParts[1])->setTimezone('America/Bogota');
        
        // Handle midnight crossover
        if ($midnightEnd <= $midnightStart) {
            $midnightEnd->addDay();
        }
        
        $this->assertTrue($midnightEnd->isAfter($midnightStart));
        $this->assertEquals($slotDate, $midnightStart->toDateString());
        $this->assertEquals(Carbon::parse($slotDate, 'America/Bogota')->addDay()->toDateString(), $midnightEnd->toDateString());
    }
}
