<?php

namespace App\Http\Controllers;

use App\Booking;
use App\Http\Controllers\Controller;
use App\Http\Requests\CalendarRequest;
use App\Listing;
use App\Models\ReservationDate;
use App\Services\CalendarService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class CalendarController extends Controller
{
    function __construct(protected CalendarService $calendarService) {}

    function index()
    {
        if (auth()->user()->hasRole(["service"])) {
            $listings = Listing::where("user_id", auth()->id())->active()->latest()->get(["id", "ids", "name", "internal_name"]);
        } else {
            $listings = Listing::active()->latest()->get(["id", "ids", "name"]);
        }
        $listing_ids = $listings->pluck("id")->toArray();
        $bookings = Booking::where("status", 0)->whereIn("listing_id", $listing_ids)->with('listing')->with('customer')->get();

        // Ensure booking dates are in the correct timezone (Columbia)
        $bookings->each(function ($booking) {
            if ($booking->check_in) {
                $booking->check_in = \Carbon\Carbon::parse($booking->check_in)->setTimezone('America/Bogota')->toDateString();
            }
            if ($booking->check_out) {
                $booking->check_out = \Carbon\Carbon::parse($booking->check_out)->setTimezone('America/Bogota')->toDateString();
            }
        });

        $reservation_dates = ReservationDate::whereIn("listing_id", $listing_ids)->get();
        return view("calendar.index", compact("listings", "bookings", "reservation_dates"));
    }
    function block_date(CalendarRequest $request)
    {
        try {
            $request_data = $request->validated();
            $this->calendarService->block_dates($request_data);
            return api_response(true, "Block dates stored");
        } catch (\Throwable $th) {
            return api_response(false, $th->getMessage());
        }
    }

    function unblock_date(Request $request)
    {
        try {
            $request->validate([
                "listing_ids" => "required|array",
                "listing_ids.*" => "exists:listings,ids",
                "start_date" => "required|date",
                "end_date" => "required|date"
            ]);
            $request_data = $request->all();
            $this->calendarService->unblock_dates($request_data);
            return api_response(true, "Block dates deleted");
        } catch (\Throwable $th) {
            return api_response(false, $th->getMessage());
        }
    }


    public function updateBlockDateNote(CalendarRequest $request)
    {
        try {
            $validated = $request->validated();

            // Parse dates with proper timezone
            $start_date = Carbon::parse($validated['start_date'])->setTimezone(config('app.timezone'));
            $end_date = Carbon::parse($validated['end_date'])->setTimezone(config('app.timezone'));

            $query = ReservationDate::whereIn('listing_id', $validated['listing_ids'])
                ->whereDate('date', '>=', $start_date->toDateString())
                ->whereDate('date', '<=', $end_date->toDateString());

            $updated = $query->update(['note' => $validated['note']]);

            return response()->json([
                'status' => true,
                'message' => 'Note updated successfully',
                'updated_count' => $updated
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    public function downloadIcs()
    {
        try {
            return $this->calendarService->downloadIcs();
        } catch (\Throwable $th) {
            return api_response_json(false, $th->getMessage());
        }
    }
}
