@extends('layouts.master')
@push('css')
    <style>
        .info-box .info-count {
            margin-top: 0px !important;
        }
    </style>
@endpush
@section('content')
    @if (auth()->user()->hasRole('admin'))
        <div class="row m-0">
            <div class="col-md-3 col-sm-6 info-box">
                <div class="media">
                    <div class="media-left">
                        <span class="icoleaf bg-primary text-white"><i
                                class="mdi mdi-checkbox-marked-circle-outline"></i></span>
                    </div>
                    <div class="media-body">
                        <h3 class="info-count text-blue">154</h3>
                        <p class="info-text font-12">Bookings</p>
                        <span class="hr-line"></span>
                        <p class="info-ot font-15">Target<span class="label label-rounded label-success">300</span></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 info-box">
                <div class="media">
                    <div class="media-left">
                        <span class="icoleaf bg-primary text-white"><i class="mdi mdi-comment-text-outline"></i></span>
                    </div>
                    <div class="media-body">
                        <h3 class="info-count text-blue">68</h3>
                        <p class="info-text font-12">Complaints</p>
                        <span class="hr-line"></span>
                        <p class="info-ot font-15">Total Pending<span class="label label-rounded label-danger">154</span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 info-box">
                <div class="media">
                    <div class="media-left">
                        <span class="icoleaf bg-primary text-white"><i class="mdi mdi-coin"></i></span>
                    </div>
                    <div class="media-body">
                        <h3 class="info-count text-blue">&#36;9475</h3>
                        <p class="info-text font-12">Earning</p>
                        <span class="hr-line"></span>
                        <p class="info-ot font-15">March : <span class="text-blue font-semibold">&#36;514578</span></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 info-box b-r-0">
                <div class="media">
                    <div class="media-left p-r-5">
                        <div id="earning" class="e" data-percent="60">
                            <div id="pending" class="p" data-percent="55"></div>
                            <div id="booking" class="b" data-percent="50"></div>
                        </div>
                    </div>
                    <div class="media-body">
                        <h2 class="text-blue font-22 m-t-0">Report</h2>
                        <ul class="p-0 m-b-20">
                            <li><i class="fa fa-circle m-r-5 text-primary"></i>60% Earnings</li>
                            <li><i class="fa fa-circle m-r-5 text-primary"></i>55% Pending</li>
                            <li><i class="fa fa-circle m-r-5 text-info"></i>50% Bookings</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-8 col-sm-12">
                    <div class="white-box stat-widget">
                        <div class="row">
                            <div class="col-md-3 col-sm-3">
                                <h4 class="box-title">Statistics</h4>
                            </div>
                            <div class="col-md-9 col-sm-9">
                                <select class="custom-select">
                                    <option selected value="0">Feb 04 - Mar 03</option>
                                    <option value="1">Mar 04 - Apr 03</option>
                                    <option value="2">Apr 04 - May 03</option>
                                    <option value="3">May 04 - Jun 03</option>
                                </select>
                                <ul class="list-inline">
                                    <li>
                                        <h6 class="font-15"><i class="fa fa-circle m-r-5 text-success"></i>New Sales
                                        </h6>
                                    </li>
                                    <li>
                                        <h6 class="font-15"><i class="fa fa-circle m-r-5 text-primary"></i>Existing
                                            Sales</h6>
                                    </li>
                                </ul>
                            </div>
                            <div class="stat chart-pos"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-12">
                    <div class="white-box">
                        <h4 class="box-title">Task Progress</h4>
                        <div class="task-widget t-a-c">
                            <div class="task-chart" id="sparklinedashdb"></div>
                            <div class="task-content font-16 t-a-c">
                                <div class="col-sm-6 b-r">
                                    Urgent Tasks
                                    <h1 class="text-primary">05 <span class="font-16 text-muted">Tasks</span></h1>
                                </div>
                                <div class="col-sm-6">
                                    Normal Tasks
                                    <h1 class="text-primary">03 <span class="font-16 text-muted">Tasks</span></h1>
                                </div>
                            </div>
                            <div class="task-assign font-16">
                                Assigned To
                                <ul class="list-inline">
                                    <li class="p-l-0">
                                        <img src="{{ asset('plugins/images/users/1.png') }}" alt="user"
                                            data-toggle="tooltip" data-placement="top" title=""
                                            data-original-title="Steave">
                                    </li>
                                    <li>
                                        <img src="{{ asset('plugins/images/users/2.png') }}" alt="user"
                                            data-toggle="tooltip" data-placement="top" title=""
                                            data-original-title="Steave">
                                    </li>
                                    <li>
                                        <img src="{{ asset('plugins/images/users/3.png') }}" alt="user"
                                            data-toggle="tooltip" data-placement="top" title=""
                                            data-original-title="Steave">
                                    </li>
                                    <li class="p-r-0">
                                        <a href="javascript:void(0);" class="btn btn-success font-16">3+</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 col-sm-12">
                    <div class="white-box bg-primary color-box">
                        <h1 class="text-white font-light">&#36;6547 <span class="font-14">Revenue</span></h1>
                        <div class="ct-revenue chart-pos"></div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="white-box bg-success color-box">
                        <h1 class="text-white font-light m-b-0">5247</h1>
                        <span class="hr-line"></span>
                        <p class="cb-text">current visits</p>
                        <h6 class="text-white font-semibold">+25% <span class="font-light">Last Week</span></h6>
                        <div class="chart">
                            <div class="ct-visit chart-pos"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="white-box bg-danger color-box">
                        <h1 class="text-white font-light m-b-0">25%</h1>
                        <span class="hr-line"></span>
                        <p class="cb-text">Finished Tasks</p>
                        <h6 class="text-white font-semibold">+15% <span class="font-light">Last Week</span></h6>
                        <div class="chart">
                            <input class="knob" data-min="0" data-max="100" data-bgColor="#f86b4a"
                                data-fgColor="#ffffff" data-displayInput=false data-width="96" data-height="96"
                                data-thickness=".1" value="25" readonly>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="white-box user-table">
                        <div class="row">
                            <div class="col-sm-6">
                                <h4 class="box-title">Table Format/User Data</h4>
                            </div>
                            <div class="col-sm-6">
                                <ul class="list-inline">
                                    <li>
                                        <a href="javascript:void(0);" class="btn btn-default btn-outline font-16"><i
                                                class="fa fa-trash" aria-hidden="true"></i></a>
                                    </li>
                                    <li>
                                        <a href="javascript:void(0);" class="btn btn-default btn-outline font-16"><i
                                                class="fa fa-commenting" aria-hidden="true"></i></a>
                                    </li>
                                </ul>
                                <select class="custom-select">
                                    <option selected>Sort by</option>
                                    <option value="1">Name</option>
                                    <option value="2">Location</option>
                                    <option value="3">Type</option>
                                    <option value="4">Role</option>
                                    <option value="5">Action</option>
                                </select>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>
                                            <div class="checkbox checkbox-info">
                                                <input id="c1" type="checkbox">
                                                <label for="c1"></label>
                                            </div>
                                        </th>
                                        <th>Name</th>
                                        <th>Location</th>
                                        <th>Type</th>
                                        <th>Role</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="checkbox checkbox-info">
                                                <input id="c2" type="checkbox">
                                                <label for="c2"></label>
                                            </div>
                                        </td>
                                        <td><a href="javascript:void(0);" class="text-link">Daniel Kristeen</a></td>
                                        <td>Texas, US</td>
                                        <td>Posts 564</td>
                                        <td><span class="label label-success">Admin</span></td>
                                        <td>
                                            <select class="custom-select">
                                                <option value="1">Modulator</option>
                                                <option value="2">Admin</option>
                                                <option value="3">Staff</option>
                                                <option value="4">User</option>
                                                <option value="5">General</option>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="checkbox checkbox-info">
                                                <input id="c3" type="checkbox">
                                                <label for="c3"></label>
                                            </div>
                                        </td>
                                        <td><a href="javascript:void(0);" class="text-link">Hanna Gover</a></td>
                                        <td>Los Angeles, US</td>
                                        <td>Posts 451</td>
                                        <td><span class="label label-info">Staff</span></td>
                                        <td>
                                            <select class="custom-select">
                                                <option value="1">Modulator</option>
                                                <option value="2">Admin</option>
                                                <option value="3">Staff</option>
                                                <option value="4">User</option>
                                                <option value="5">General</option>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="checkbox checkbox-info">
                                                <input id="c4" type="checkbox">
                                                <label for="c4"></label>
                                            </div>
                                        </td>
                                        <td><a href="javascript:void(0);" class="text-link">Jeffery Brown</a></td>
                                        <td>Houston, US</td>
                                        <td>Posts 978</td>
                                        <td><span class="label label-danger">User</span></td>
                                        <td>
                                            <select class="custom-select">
                                                <option value="1">Modulator</option>
                                                <option value="2">Admin</option>
                                                <option value="3">Staff</option>
                                                <option value="4">User</option>
                                                <option value="5">General</option>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="checkbox checkbox-info">
                                                <input id="c5" type="checkbox">
                                                <label for="c5"></label>
                                            </div>
                                        </td>
                                        <td><a href="javascript:void(0);" class="text-link">Elliot Dugteren</a></td>
                                        <td>San Antonio, US</td>
                                        <td>Posts 34</td>
                                        <td><span class="label label-warning">General</span></td>
                                        <td>
                                            <select class="custom-select">
                                                <option value="1">Modulator</option>
                                                <option value="2">Admin</option>
                                                <option value="3">Staff</option>
                                                <option value="4">User</option>
                                                <option value="5">General</option>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="checkbox checkbox-info">
                                                <input id="c6" type="checkbox">
                                                <label for="c6"></label>
                                            </div>
                                        </td>
                                        <td><a href="javascript:void(0);" class="text-link">Sergio Milardovich</a></td>
                                        <td>Jacksonville, US</td>
                                        <td>Posts 31</td>
                                        <td><span class="label label-primary">Partial</span></td>
                                        <td>
                                            <select class="custom-select">
                                                <option value="1">Modulator</option>
                                                <option value="2">Admin</option>
                                                <option value="3">Staff</option>
                                                <option value="4">User</option>
                                                <option value="5">General</option>
                                            </select>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <ul class="pagination">
                            <li class="disabled"><a href="#">1</a></li>
                            <li class="active"><a href="#">2</a></li>
                            <li><a href="#">3</a></li>
                            <li><a href="#">4</a></li>
                            <li><a href="#">5</a></li>
                        </ul>
                        <a href="javascript:void(0);" class="btn btn-success pull-right m-t-10 font-20">+</a>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="white-box">
                        <div class="task-widget2">
                            <div class="task-image">
                                <img src="{{ asset('plugins/images/task.jpg') }}" alt="task" class="img-responsive">
                                <div class="task-image-overlay"></div>
                                <div class="task-detail">
                                    <h2 class="font-light text-white m-b-0">07 April</h2>
                                    <h4 class="font-normal text-white m-t-5">Your tasks for today</h4>
                                </div>
                                <div class="task-add-btn">
                                    <a href="javascript:void(0);" class="btn btn-success">+</a>
                                </div>
                            </div>
                            <div class="task-total">
                                <p class="font-16 m-b-0"><strong>5</strong> Tasks for <a href="javascript:void(0);"
                                        class="text-link">Jon Doe</a>
                                </p>
                            </div>
                            <div class="task-list">
                                <ul class="list-group">
                                    <li class="list-group-item bl-info">
                                        <div class="checkbox checkbox-success">
                                            <input id="c7" type="checkbox">
                                            <label for="c7">
                                                <span class="font-16">Create invoice for customers and email each
                                                    customers.</span>
                                            </label>
                                            <h6 class="p-l-30 font-bold">05:00 PM</h6>
                                        </div>
                                    </li>
                                    <li class="list-group-item bl-warning">
                                        <div class="checkbox checkbox-success">
                                            <input id="c8" type="checkbox" checked>
                                            <label for="c8">
                                                <span class="font-16">Send payment of <strong>&#36;500 invoised</strong> on
                                                    23 May to <a href="javascript:void(0);" class="text-link">Daniel
                                                        Kristeen</a> via paypal.</span>
                                            </label>
                                            <h6 class="p-l-30 font-bold">03:00 PM</h6>
                                        </div>
                                    </li>
                                    <li class="list-group-item bl-danger">
                                        <div class="checkbox checkbox-success">
                                            <input id="c9" type="checkbox">
                                            <label for="c9">
                                                <span class="font-16">It is a long established fact that a reader will be
                                                    distracted by the readable.</span>
                                            </label>
                                            <h6 class="p-l-30 font-bold">04:45 PM</h6>
                                        </div>
                                    </li>
                                    <li class="list-group-item bl-success">
                                        <div class="checkbox checkbox-success">
                                            <input id="c10" type="checkbox">
                                            <label for="c10">
                                                <span class="font-16">It is a long established fact that a reader will be
                                                    distracted by the readable.</span>
                                            </label>
                                            <h6 class="p-l-30 font-bold">05:30 PM</h6>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <div class="task-loadmore">
                                <a href="javascript:void(0);" class="btn btn-default btn-outline btn-rounded">Load
                                    More</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="white-box chat-widget">
                        <a href="javascript:void(0);" class="pull-right"><i class="icon-settings"></i></a>
                        <h4 class="box-title">Chat</h4>
                        <ul class="chat-list slimscroll" style="overflow: hidden;" tabindex="5005">
                            <li>
                                <div class="chat-image"><img alt="male"
                                        src="{{ asset('plugins/images/users/hanna.jpg') }}"></div>
                                <div class="chat-body">
                                    <div class="chat-text">
                                        <p><span class="font-semibold">Hanna Gover</span> Hey Daniel, This is just a
                                            sample chat. </p>
                                    </div>
                                    <span>2 Min ago</span>
                                </div>
                            </li>
                            <li class="odd">
                                <div class="chat-body">
                                    <div class="chat-text">
                                        <p> buddy </p>
                                    </div>
                                    <span>2 Min ago</span>
                                </div>
                            </li>
                            <li>
                                <div class="chat-image"><img alt="male"
                                        src="{{ asset('plugins/images/users/hanna.jpg') }}"></div>
                                <div class="chat-body">
                                    <div class="chat-text">
                                        <p><span class="font-semibold">Hanna Gover</span> Bye now. </p>
                                    </div>
                                    <span>1 Min ago</span>
                                </div>
                            </li>
                            <li class="odd">
                                <div class="chat-body">
                                    <div class="chat-text">
                                        <p> We have been busy all the day to make your website proposal and finally came
                                            with the super excited offer. </p>
                                    </div>
                                    <span>5 Sec ago</span>
                                </div>
                            </li>
                        </ul>
                        <div class="chat-send">
                            <input type="text" class="form-control" placeholder="Write your message">
                            <i class="fa fa-camera"></i>
                        </div>
                    </div>
                </div>
            </div>
            <!-- ===== Right-Sidebar ===== -->
            {{-- @include('layouts.partials.right-sidebar') --}}
            <!-- ===== Right-Sidebar-End ===== -->
        </div>
    @elseif(auth()->user()->hasRole(['user', 'sub_admin']))
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="parent-dash-div">
                        <div class="index-head">
                            <h1>{{ translate('dashboard.dashboard') }}</h1>
                        </div>
                        {{-- <div class="form-group has-feedback has-search">
                                <form class="example" action="/action_page.php" style="width: 100%">
                                    <button type="submit"><i class="fa fa-search"></i></button>
                                    <input type="text" placeholder="Search.." name="search2">
                                </form>
                            </div> --}}
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12 ">
                    {{-- <div class=" index"> --}}
                    <div class=" main_cart_sec">
                        <div class="card_wrapper">
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard.total_listings') }}</p>
                                        <h1 class="semiBold_fontweight">{{ App\Listing::count() }}</h1>
                                    </div>
                                    <div class="card_inners">
                                        <div class="card_pill black_card_pill">
                                            <h1 class="notranslate">L</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard.active_bookings') }}</p>
                                        <h1 class="semiBold_fontweight">{{ count($bookings) }}</h1>
                                    </div>
                                    <div class="card_inners">
                                        <div class="card_pill yellow_card_pill">
                                            <h1 class="notranslate">B</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard.pending_reports') }}</p>
                                        <h1 class="semiBold_fontweight">{{ App\Report::where('status', 0)->count() }}</h1>
                                    </div>
                                    <div class="card_inners">
                                        <div class="card_pill black_card_pill">
                                            <h1 class="notranslate">R</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard.lifetime_commisions') }}</p>
                                        <h1 class="semiBold_fontweight">
                                            ${{ number_format(App\Booking::sum('commission_amount'), 0, '.', ',') }}</h1>
                                    </div>
                                    <div class="card_inners">
                                        <div class="card_pill yellow_card_pill">
                                            <h1 class="notranslate">C</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard.withdrawal_requests') }}</p>
                                        <h1 class="semiBold_fontweight">
                                            ${{ number_format(App\WithdrawalRequest::where('status', 0)->sum('amount'), 0, '.', ',') }}
                                        </h1>
                                    </div>
                                    <div class="card_inners">
                                        <div class="card_pill yellow_card_pill">
                                            <h1 class="notranslate">R</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard.total_bookings') }}</p>
                                        <h1 class="semiBold_fontweight">{{ App\Booking::count() }}</h1>
                                    </div>
                                    <div class="card_inners">
                                        <div class="card_pill black_card_pill">
                                            <h1 class="notranslate">B</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard.life_time_earning') }}</p>
                                        <h1 class="semiBold_fontweight notranslate">
                                            ${{ number_format(App\Booking::sum('total_amount'), 0, '.', ',') }} COP</h1>
                                    </div>
                                    <div class="card_inners">
                                        <div class="card_pill yellow_card_pill">
                                            <h1 class="notranslate">E</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {{-- </div> --}}
                </div>
            </div>
        </div>
        <section class="rep_not dash_index_report_notification_sec">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-7 col_left">
                        <div class="table_sect">
                            <div class="table_wrraper">
                                <div class="tabel_hdng_icon">
                                    <!-- <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i> -->
                                </div>
                                <div class="table-responsive">
                                    <table id="myTable" class="table  custom_table">
                                        <thead>
                                            <tr>
                                                <th scope="col">{{ translate('dashboard.reported_by') }}</th>
                                                <th scope="col">{{ translate('dashboard.reported_by') }}</th>
                                                <th scope="col">{{ translate('dashboard.date') }}</th>
                                                <th scope="col">{{ translate('dashboard.status') }}</th>
                                                <th scope="col">{{ translate('dashboard.description') }}</th>
                                                <th scope="col">{{ translate('dashboard.action') }}
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse (App\Report::take(4)->get() as $report)
                                                <tr>
                                                    <td>{{ $report->user->name ?? '-' }}</td>
                                                    <td>{{ $report->subject }}</td>
                                                    <td>{{ $report->created_at }}</td>
                                                    <td>
                                                        {{-- <div class="btn-group trans">
                                                            <select name="cars" id="cars">
                                                                <option value="pending">Pending</option>
                                                                <option value="resolved">Resolved</option>
                                                            </select>
                                                        </div> --}}
                                                        @if ($report->status == 0)
                                                            {{ translate('dashboard.pending') }}
                                                        @else
                                                            {{ trans('dashboard.approved') }}
                                                        @endif
                                                    </td>
                                                    <td>{{ $report->description }}</td>
                                                    <td class="form_btn ">
                                                        <div class="dropdown">
                                                            <button class=" dropdown-toggle" type="button"
                                                                id="dropdownMenuButton" data-toggle="dropdown"
                                                                aria-haspopup="true" aria-expanded="false">
                                                                <i class="fa-solid fa-ellipsis"
                                                                    style="color: #a0aec0;"></i>
                                                            </button>
                                                            <div class="dropdown-menu"
                                                                aria-labelledby="dropdownMenuButton">
                                                                <button class="dropdown-item ">
                                                                    {{ translate('dashboard.view') }}</button>
                                                                <button class="dropdown-item " data-toggle="modal"
                                                                    data-target="#repModal"> {{ translate('dashboard.reply') }}</button>
                                                                <button
                                                                    class="dropdown-item ">{{ translate('dashboard.delete') }}</button>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="8">
                                                        <h3 class="text-center">{{ translate('dashboard.no_report_found') }}</h3>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-5 col_right p-0">
                        <div class="notify-box">
                            <div class="white-box comment-widget">
                                <div class="box-title">
                                    <h2>{{ translate('dashboard.notifications') }}</h2>
                                </div>
                                <ul class="media-list">
                                    @forelse (auth()->user()->notifications->take(02) as $notification)
                                        <li class="media">
                                            <div class="media-left">
                                                @if ($notification->read_at == null)
                                                    <i class="fa-solid fa-circle" style="color: #ffce32;"></i>
                                                @endif
                                            </div>
                                            <div class="media-body">
                                                @php
                                                    $data = translateNotification($notification->data);
                                                @endphp
                                                <div class="media-heading justify-content-between align-items-center d-flex m-0">
                                                    <p style="color: #4A4A4A !important;" class="text-link m-0">
                                                        {{ $data['title'] ?? '-' }}
                                                    </p>
                                                    <ul class="list-inline">
                                                        <span class="font-normal com-time" style="color: #4A4A4A; font-family: 'Poppins-Medium'; font-size: 12px; font-weight: 400; line-height: 17px;">
                                                            {{ $notification->created_at->format('d-M-Y h:i:s') ?? '-' }}
                                                        </span>
                                                    </ul>
                                                </div>
                                                <a style="color: #9B9B9B; font-size: 13px;">{!! $data['message'] ?? '-' !!}</a>
                                            </div>
                                        </li>
                                    @empty
                                        <li class="text-center poppins-medium text-dark-black no-notification">
                                            {{ translate('dashboard.no_new_notification_found') }}
                                        </li>
                                    @endforelse
                                </ul>
                                <div class="top-border">
                                    {{-- <a href=""  >View All Notifications</a> --}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="table_sect tab_fixed">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <div class="table_wrraper">
                            <ul class="nav nav-pills">
                                <li class="active"><a data-toggle="pill"
                                        href="#Widthdrawal">{{ translate('dashboard.withdrawal_requests') }}</a></li>
                                <li><a data-toggle="pill" href="#Payments">{{ translate('dashboard.payments') }}</a></li>
                            </ul>
                            <div class="tab-content">
                                <div id="Widthdrawal" class="tab-pane fade in active">
                                    <h1>{{ translate('dashboard.withdrawal_requests')  }}</h1>
                                    <div class="table-responsive">
                                        <table id="myTable" class="table  custom_table">
                                            <thead>
                                                <tr>
                                                    <th scope="col" style="width: 130px;">{{ translate('dashboard.profile_name') }}
                                                    </th>
                                                    <th scope="col" style="width: 120px;">{{ translate('dashboard.date') }}</th>
                                                    <th scope="col" style="width: 120px;">{{ translate('dashboard.payments') }}</th>
                                                    <th scope="col" style="width: 160px;">{{ translate('dashboard.status') }}</th>
                                                    <th scope="col" style="width: 430px;">{{ translate('dashboard.message') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse ($withdrawal_requests as $withdrawal_request)
                                                    <tr>
                                                        <td>{{ $withdrawal_request->user->name ?? '-' }}</td>
                                                        <td>{{ $withdrawal_request->created_at->format('M/d') }}</td>
                                                        <td>${{ $withdrawal_request->amount }}</td>
                                                        <td class="text-success">Conditions Met</td>
                                                        <td>{{ $withdrawal_request->description ?? translate('dashboard.no_message') }}</td>
                                                        <td class="form_btn d-flex gap-1">
                                                            <div class="form_black_btn">
                                                                <button class="btn  btn-sm"
                                                                    onclick="location.href='pay-withdrawal-req/{{ $withdrawal_request->id }}'">
                                                                    {{ translate('dashboard.pay_now') }}
                                                                </button>
                                                            </div>
                                                            <div class="form_yellow_btn">
                                                                <button class="btn btn-sm wr-reject-btn"
                                                                    data-toggle="modal"
                                                                    data-withdrawal-request-id="{{ $withdrawal_request->id }}"
                                                                    data-target="#rejModal" data-whatever="@mdo">
                                                                    {{ trans('reject') }}</button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="5" class="text-center">
                                                            <h2>
                                                                {{ translate('dashboard.no_withdrawal_request') }}
                                                            </h2>
                                                        </td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div id="Payments" class="tab-pane fade">
                                    <h1>{{ translate('dashboard.payments') }}</h1>
                                    <div class="table-responsive">
                                        <table id="myTable" class="table  custom_table">
                                            <thead>
                                                <tr>
                                                    <th scope="col" style="width: 120px;">{{ translate('dashboard.profile_name') }}
                                                    </th>
                                                    <th scope="col" style="width: 120px;">{{ translate('dashboard.date') }}</th>
                                                    <th scope="col" style="width: 120px;">{{ translate('dashboard.payments') }}</th>
                                                    <th scope="col" style="width: 160px;">{{ translate('dashboard.status') }}</th>
                                                    <th scope="col" style="width: 480px;">{{ translate('dashboard.message') }}</th>
                                                    <th scope="col" style="width: 160px;">{{ translate('dashboard.view_invoice') }}
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php for($i=0; $i<=1; $i++) { ?>
                                                @if ($i % 2 == 0)
                                                    <tr>
                                                        <td>John Doe</td>
                                                        <td>10/10</td>
                                                        <td>$200</td>
                                                        <td class="text-success">Conditions Met</td>
                                                        <td>Lorem Ipsum is simply dummy text of the printing and typesetting
                                                            industry.</td>
                                                        <td class="form_btn ">
                                                            <a href="{{ asset('website') }}/pdf/new.pdf">
                                                                <img src="{{ asset('website') }}/images/pdf.png"
                                                                    alt="">
                                                            </a>
                                                        </td>
                                                    </tr>
                                                @else
                                                    <tr>
                                                        <td>John Doe</td>
                                                        <td>10/10</td>
                                                        <td>$200</td>
                                                        <td class="text-danger">Conditions Met</td>
                                                        <td>Lorem Ipsum is simply dummy text of the printing and typesetting
                                                            industry.</td>
                                                        <td class="form_btn ">
                                                            <a href="{{ asset('website') }}/pdf/new.pdf">
                                                                <img src="{{ asset('website') }}/images/pdf.png"
                                                                    alt="">
                                                            </a>
                                                        </td>
                                                    </tr>
                                                @endif
                                                <?php } ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        {{-- Modal --}}
        <section class=" reject">
            <div class="modal fade" id="rejModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
                <div class="modal-dialog" id="myModal" role="document">
                    <form action="{{ url('withdrawal-request-reject') }}" method="POST">
                        @csrf
                        <div class="modal-content">
                            <div class="modal-body">
                                <span class="close" data-dismiss="modal">&times;</span>
                                <h1 class="modal-title grey-color" id="exampleModalLabel1">{{ trans('reject') }}
                                </h1>
                                <div class="form_field_padding">
                                    <input type="hidden" name="withdrawal_req_id" id="withdrawal_req_id">
                                    <div class="mb-3">
                                        <textarea class="form-control" name="reason" id="" rows="10" column="25"
                                            placeholder="Type Message"></textarea>
                                    </div>
                                </div>
                                <div class=" modal_btn  ">
                                    <button type="button " class="btn yellow">{{ trans('send') }}</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </section>
        {{-- Modal --}}
        <section class=" reject">
            <div class="modal fade" id="repModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
                <div class="modal-dialog" id="myModal" role="document">
                    <div class="modal-content">
                        <div class="modal-body">
                            <span class="close" data-dismiss="modal">&times;</span>
                            <h1 class="modal-title grey-color" id="exampleModalLabel1">Reply</h1>
                            <div class="form_field_padding">
                                <div class="mb-3 mod_cust_text">
                                    <p>Reported By:<span> John Doe</span></p>
                                    <p>Subject:<span> Lorem Ipsum</span></p>
                                    <p>Description:<span> Lorem Ipsum is simply dummy text of the printing and typesetting
                                            industry. Lorem Ipsum has been the industry's standard dummy text ever since the
                                            1500s</span></p>
                                </div>
                            </div>
                            <div class="form_field_padding">
                                <div class="mb-3">
                                    <textarea class="form-control" name="" id="" rows="10" column="25"
                                        placeholder="Type Message"></textarea>
                                </div>
                            </div>
                            <div class=" modal_btn  ">
                                <button type="button " class="btn yellow">Send</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    @elseif(auth()->user()->hasRole('service'))
    
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="index-head">
                        <h1 class="grey-color">{{ translate('dashboard.dashboard') }}</h1>
                    </div>
                </div>
            </div>
        </div>

        <section class="service_provider_index_sec">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-6 ">
                        <div class=" index">
                            <div class=" main_cart_sec">
                                <div class="card_wrapper">
                                    <div class="card_element">
                                        <div class="card_parent white_card">
                                            <div class="card_inners">
                                                <p>{{ translate('dashboard.total_listings') }}</p>
                                                <h1 class="grey-color Bold_fontweight">
                                                    {{ App\Listing::where('user_id', auth()->id())->count() }}</h1>
                                            </div>
                                            <div class="card_inners">
                                                <div class="card_pill yellow_card_pill">
                                                    <h1 class="notranslate">@if(app()->getLocale() == 'en')L @else A @endif</h1>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card_element">
                                        <div class="card_parent white_card">
                                            <div class="card_inners">
                                                <p>{{ translate('dashboard.host_reviews') }}</p>
                                                <h1 class="grey-color Bold_fontweight">
                                                    {{ App\Review::join('listings', 'reviews.listing_id', '=', 'listings.id')->where('listings.user_id', auth()->id())->count() ?? 0 }}
                                                </h1>
                                            </div>
                                            <div class="card_inners">
                                                <div class="card_pill yellow_card_pill">
                                                    <h1 class="notranslate">R</h1>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card_element">
                                        <div class="card_parent white_card">
                                            <div class="card_inners">
                                                <p>{{ translate('dashboard.life_time_earning') }}
                                                </p>
                                                <h1 class="grey-color Bold_fontweight notranslate">
                                                    ${{ number_format(App\Booking::where('provider_id', auth()->id())->sum('total_amount') ?? '0', 0, '.', ',') }}
                                                    COP
                                                </h1>
                                            </div>
                                            <div class="card_inners">
                                                <div class="card_pill black_card_pill">
                                                    <h1 class="notranslate">@if(app()->getLocale() == 'en')E @else G @endif</h1>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card_element">
                                        <div class="card_parent white_card">
                                            <div class="card_inners">
                                                <p>{{ translate('dashboard.overall_bookings') }}
                                                </p>
                                                <h1 class="grey-color Bold_fontweight">
                                                    {{ App\Booking::where('provider_id', auth()->id())->count() }}</h1>
                                            </div>
                                            <div class="card_inners">
                                                <div class="card_pill black_card_pill">
                                                    <h1 class="notranslate">@if(app()->getLocale() == 'en')B @else R @endif</h1>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 ">
                        <div class="notify-box">
                            <div class="white-box comment-widget">
                                <div class="box-title">
                                    <h2>{{ translate('dashboard.notifications') }}
                                    </h2>
                                </div>
                                <ul class="media-list">
                                    {{-- @forelse (auth()->user()->notifications->take(3) as $notification)
                                        <li class="media">
                                            <div class="media-left">
                                                @if ($notification->read_at == null)
                                                    <i class="fa-solid fa-circle" style="color: #ffce32;"></i>
                                                @endif
                                            </div>
                                            <div class="media-body">
                                                <div
                                                    class="media-heading justify-content-between align-items-center d-flex m-0">
                                                    <p href="javascript:void(0);" style="color: #4A4A4A !important;"
                                                        class="text-link m-0">{{ $notification->data['title'] ?? '-' }}</p>
                                                    <ul class="list-inline">
                                                        @if ($notification->read_at == null)
                                                            <a href="{{ route("markAsRead", $notification->id) }}" class="btn btn-sm btn-warning " style="margin-left: 10px">
                                                                Mark as read
                                                            </a>
                                                        @endif
                                                        <span class="font-normal com-time" style="color: #4A4A4A; font-family: 'Poppins-Medium'; font-size: 12px; font-weight: 400; line-height: 17px;">{{ $notification->created_at->format('d-M-Y h:i:s') ?? '-' }}</span>
                                                    </ul>
                                                </div>
                                                <a style="color: #9B9B9B; font-size: 13px;">{!! $notification->data['message'] ?? '-' !!}</a>
                                            </div>
                                        </li>
                                    @empty
                                        <li class="text-center poppins-medium text-dark-black no-notification">{{ translate('dashboard.no_new_notification_found') }}</li>
                                    @endforelse --}}
                                    @forelse (auth()->user()->notifications->take(02) as $notification)
                                            <li class="media">
                                                <div class="media-left">
                                                    @if ($notification->read_at == null)
                                                        <i class="fa-solid fa-circle" style="color: #ffce32;"></i>
                                                    @endif
                                                </div>
                                                <div class="media-body">
                                                    @php
                                                        $data = translateNotification($notification->data);
                                                    @endphp
                                                    <div class="media-heading justify-content-between align-items-center d-flex m-0">
                                                        <p style="color: #4A4A4A !important;" class="text-link m-0">
                                                            {{ $data['title'] ?? '-' }}
                                                        </p>
                                                        <ul class="list-inline">
                                                            <span class="font-normal com-time" style="color: #4A4A4A; font-family: 'Poppins-Medium'; font-size: 12px; font-weight: 400; line-height: 17px;">
                                                                {{ $notification->created_at->format('d-M-Y h:i:s') ?? '-' }}
                                                            </span>
                                                        </ul>
                                                    </div>
                                                    <a style="color: #9B9B9B; font-size: 13px;">{!! $data['message'] ?? '-' !!}</a>
                                                </div>
                                            </li>
                                        @empty
                                            <li class="text-center poppins-medium text-dark-black no-notification">
                                                {{ translate('dashboard.no_new_notification_found') }}
                                            </li>
                                        @endforelse
                                </ul>
                                <div class="top-border">
                                    {{-- <a href=""  >View All Notifications</a> --}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <section class="table_sect tab_fixed tabs_sec index_service_provider_bookings">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <div class="table_wrraper">
                            <div class="tabel_hdng_icon">
                                <h1 class="grey-color">{{ translate('dashboard.bookings') }}</h1>
                            </div>
                            <ul class="nav nav-pill d-flex gap-1 align-items-center justify-content-end">
                                <li class="active">
                                    <a data-toggle="pill" href="#today" class="nav-pill-btn">
                                        {{ translate('dashboard.today') }}
                                    </a>
                                </li>
                                <li><a class="nav-pill-btn" data-toggle="pill" href="#tomorrow">{{ translate('dashboard.upcoming') }}</a>
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active" id="today">
                                    <div class="table-responsive">
                                        <table id="myTable" class="table service_table  custom_table">
                                            <thead>
                                                <tr>
                                                    <th scope="col">{{ translate('dashboard.booking_id') }}</th>
                                                    <th scope="col">{{ translate('dashboard.listing') }}</th>
                                                    <th scope="col">{{ translate('dashboard.customer') }}</th>
                                                    <th scope="col">{{ translate('dashboard.category') }}</th>
                                                    <th scope="col">{{ translate('dashboard.from') }}</th>
                                                    <th scope="col">{{ translate('dashboard.until') }}</th>
                                                    <th scope="col">{{ translate('dashboard.total_paid_customer') }}</th>
                                                    <th scope="col">{{ translate('dashboard.total_payout') }}</th>
                                                    <th scope="col">{{ translate('dashboard.booked') }}</th>
                                                    <th scope="col">{{ translate('dashboard.status') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse ($today_bookings as $booking)
                                                    <tr>
                                                        <td>{{ $booking->booking_number ?? '-' }}</td>
                                                        <td>{{ $booking->listing->name ?? '-' }}</td>
                                                        <td>{{ $booking->customer->name ?? '-' }}</td>
                                                        <td>{{ $booking->listing->category->display_name ?? '-' }}</td>
                                                        <td>
                                                            {{ date(config('constant.date_format'), strtotime($booking->check_in)) }}

                                                        </td>
                                                        <td>
                                                            {{ date(config('constant.date_format'), strtotime($booking->check_out)) }}

                                                        </td>
                                                        <td>COP
                                                            {{ isset($booking->total_amount) ? number_format($booking->total_amount, 0) : '-' }}
                                                        </td>
                                                        <td>
                                                            COP
                                                            {{-- {{ isset($booking->total_amount) ? number_format($booking->total_amount, 0) : '-' }} --}}
                                                            @php
                                                                $deductedAmount =
                                                                    ($booking->total_amount ?? 0) *
                                                                    (($booking->listing->category->tax ?? 0) / 100);
                                                                $totalPayout = $booking->total_amount - $deductedAmount;
                                                            @endphp
                                                            {{ isset($totalPayout) ? number_format($totalPayout) : '-' }}
                                                        </td>
                                                        <td>{{ date(config('constant.date_format'), strtotime($booking->created_at)) }}
                                                        </td>
                                                        <td>
                                                            {{-- <span
                                                                style="@if (strtolower($booking->statusName->name) == 'pending') color: #ffc107; @elseif(strtolower($booking->statusName->name) == 'accept' ||
                                                                        strtolower($booking->statusName->name) == 'completed' ||
                                                                        strtolower($booking->statusName->name) == 'paid') color: green; @elseif(strtolower($booking->statusName->name) == 'rejected' || strtolower($booking->statusName->name) == 'cancelled') color: red; @else color: #0384CC; @endif"> --}}
                                                                @php
                                                                    // Use Colombia timezone consistently
                                                                    $checkInDate = \Carbon\Carbon::parse(
                                                                        $booking->check_in,
                                                                    )->setTimezone('America/Bogota')->toDateString();
                                                                    $checkOutDate = \Carbon\Carbon::parse(
                                                                        $booking->check_out,
                                                                    )->setTimezone('America/Bogota')->toDateString();
                                                                    $today = \Carbon\Carbon::now('America/Bogota')->toDateString();
                                                                    $currentTime = \Carbon\Carbon::now('America/Bogota');
                                                                    $isOnGoing = false;
                                                                    $isCompleted = false;

                                                                    // Check if the booking is for today or in the past
                                                                    if (
                                                                        $checkInDate <= $today &&
                                                                        $checkOutDate >= $today
                                                                    ) {
                                                                        // For Daily bookings, this is enough to be "On Going"
                                                                        if ($booking->listing_basis != 'Hourly') {
                                                                            $isOnGoing = true;
                                                                        } else {
                                                                            // For Hourly bookings, check if current time is within any booked slot
                                                                            $hourlySlots = $booking->hourly_slots;
                                                                            if (
                                                                                $hourlySlots &&
                                                                                $hourlySlots->count() > 0
                                                                            ) {
                                                                                $allSlotsPassed = true;

                                                                                foreach ($hourlySlots as $slot) {
                                                                                    // Parse the slot time (format is typically "21:00 - 22:00")
                                                                                    $slotParts = explode(
                                                                                        ' - ',
                                                                                        $slot->slot,
                                                                                    );
                                                                                    if (count($slotParts) == 2) {
                                                                                        $slotStartTime = \Carbon\Carbon::parse(
                                                                                            $slot->date .
                                                                                                ' ' .
                                                                                                $slotParts[0],
                                                                                        )->setTimezone('America/Bogota');
                                                                                        $slotEndTime = \Carbon\Carbon::parse(
                                                                                            $slot->date .
                                                                                                ' ' .
                                                                                                $slotParts[1],
                                                                                        )->setTimezone('America/Bogota');

                                                                                        // Handle midnight crossover (e.g., "23:00 - 00:00")
                                                                                        // If end time is earlier than start time, it means it crosses midnight
                                                                                        if ($slotEndTime <= $slotStartTime) {
                                                                                            $slotEndTime->addDay(); // Move end time to next day
                                                                                        }

                                                                                        // Check if current time is within this slot
                                                                                        if (
                                                                                            $currentTime->between(
                                                                                                $slotStartTime,
                                                                                                $slotEndTime,
                                                                                            )
                                                                                        ) {
                                                                                            $isOnGoing = true;
                                                                                            $allSlotsPassed = false;
                                                                                            break;
                                                                                        }

                                                                                        // If any slot's end time is in the future, not all slots have passed
                                                                                        if ($slotEndTime > $currentTime) {
                                                                                            $allSlotsPassed = false;
                                                                                        }
                                                                                    }
                                                                                }

                                                                                // If all slots have passed and booking is still pending, it should be completed
                                                                                if ($allSlotsPassed && $booking->status == 0) {
                                                                                    $isCompleted = true;
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                @endphp

                                                                @if ($isOnGoing)
                                                                    <span style="color: #0384CC;">
                                                                        {{translate('dashboard.on_going')}}
                                                                    </span>
                                                                @elseif ($isCompleted)
                                                                    <span style="color: green;">
                                                                        {{translate('dashboard.completed')}}
                                                                    </span>
                                                                @else
                                                                    <span style="color: #ffc107;">
                                                                        {{ $booking->statusName->name == 'Pending' ? translate('dashboard.upcoming') : $booking->statusName->name ?? '-' }}
                                                                    </span>
                                                                @endif

                                                            {{-- </span> --}}
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="10" class="text-center">{{ translate('dashboard.no_bookings_for_today') }}</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="tab-pane" id="tomorrow">
                                    <div class="table-responsive">
                                        <table id="myTable" class="table service_table  custom_table">
                                            <thead>
                                                <tr>
                                                    <th scope="col">{{ translate('dashboard.booking_id') }}</th>
                                                    <th scope="col">{{ translate('dashboard.listing') }}</th>
                                                    <th scope="col">{{ translate('dashboard.customer') }}</th>
                                                    <th scope="col">{{ translate('dashboard.category') }}</th>
                                                    <th scope="col">{{ translate('dashboard.from') }}</th>
                                                    <th scope="col">{{ translate('dashboard.until') }}</th>
                                                    <th scope="col">{{ translate('dashboard.total_paid_customer') }}</th>
                                                    <th scope="col">{{ translate('dashboard.total_payout') }}</th>
                                                    <th scope="col">{{ translate('dashboard.booked') }}</th>
                                                    <th scope="col">{{ translate('dashboard.status') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse ($tomorrow_bookings as $booking)
                                                    <tr>
                                                        <td>{{ $booking->booking_number ?? '-' }}</td>
                                                        <td>{{ $booking->listing->name ?? '-' }}</td>
                                                        <td>{{ $booking->customer->name ?? '-' }}</td>
                                                        <td>{{ $booking->listing->category->display_name ?? '-' }}</td>
                                                        <td>
                                                            {{ date(config('constant.date_format'), strtotime($booking->check_in)) }}
                                                        </td>
                                                        <td>
                                                            {{ date(config('constant.date_format'), strtotime($booking->check_out)) }}
                                                        </td>
                                                        <td>
                                                            COP
                                                            {{ isset($booking->total_amount) ? number_format($booking->total_amount, 0) : '-' }}
                                                        </td>
                                                        <td>
                                                            COP
                                                            {{-- {{ isset($item->total_amount) ? number_format($item->total_amount, 0) : '-' }} --}}
                                                            @php
                                                                $deductedAmount = ($booking->total_amount ?? 0) * (($booking->listing->category->tax ?? 0) / 100);
                                                                $totalPayout = $booking->total_amount - $deductedAmount;
                                                            @endphp
                                                            {{ isset($totalPayout) ? number_format($totalPayout) : '-' }}
                                                        </td>
                                                            <td>{{ date(config('constant.date_format'), strtotime($booking->created_at)) }}
                                                        </td>
                                                        <td>
                                                            {{-- <span
                                                                style="@if (strtolower($booking->statusName->name) == 'pending') color: #ffc107; @elseif(strtolower($booking->statusName->name) == 'accept' ||
                                                                        strtolower($booking->statusName->name) == 'completed' ||
                                                                        strtolower($booking->statusName->name) == 'paid') color: green; @elseif(strtolower($booking->statusName->name) == 'rejected' || strtolower($booking->statusName->name) == 'cancelled') color: red; @else color: #0384CC; @endif"> --}}
                                                                @php
                                                                    // Use Colombia timezone consistently
                                                                    $checkInDate = \Carbon\Carbon::parse(
                                                                        $booking->check_in,
                                                                    )->setTimezone('America/Bogota')->toDateString();
                                                                    $checkOutDate = \Carbon\Carbon::parse(
                                                                        $booking->check_out,
                                                                    )->setTimezone('America/Bogota')->toDateString();
                                                                    $today = \Carbon\Carbon::now('America/Bogota')->toDateString();
                                                                    $tomorrow = \Carbon\Carbon::now('America/Bogota')->addDay()->toDateString();
                                                                    $currentTime = \Carbon\Carbon::now('America/Bogota');
                                                                    $isOnGoing = false;
                                                                    $isCompleted = false;
                                                                    $isUpcoming = false;

                                                                    // Check if this is an ongoing booking that's ending tomorrow (extended stay checking out)
                                                                    if (
                                                                        $checkInDate <= $today &&
                                                                        $checkOutDate == $tomorrow
                                                                    ) {
                                                                        // This is an ongoing booking ending tomorrow
                                                                        $isOnGoing = true;
                                                                    }
                                                                    // Check if this is a booking starting tomorrow
                                                                    elseif ($checkInDate == $tomorrow) {
                                                                        $isUpcoming = true;
                                                                    }
                                                                @endphp

                                                                @if ($isOnGoing)
                                                                    <span style="color: #0384CC;">
                                                                        {{ translate('dashboard.on_going') }}
                                                                    </span>
                                                                @elseif ($isUpcoming)
                                                                    <span style="color: #ffc107;">
                                                                        {{ translate('dashboard.upcoming') }}
                                                                    </span>
                                                                @elseif ($isCompleted)
                                                                    <span style="color: green;">
                                                                        {{ translate('dashboard.completed') }}
                                                                    </span>
                                                                @else
                                                                    <span style="color: #ffc107;">
                                                                        {{ $booking->statusName->name ?? '-' }}
                                                                    </span>
                                                                @endif

                                                            {{-- </span> --}}
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="10" class="text-center">{{ translate('dashboard.no_tomorrow_bookings') ?? 'No bookings for tomorrow' }}
                                                        </td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    @endif
@endsection
@push('js')
    <script src="{{ asset('js/db1.js') }}"></script>

    <script>
        $(document).ready(function() {
            $(".wr-reject-btn").on("click", function() {
                let withdrawal_request_id = $(this).data("withdrawal-request-id");
                $("#withdrawal_req_id").val(withdrawal_request_id);
            });
        })
    </script>
@endpush
